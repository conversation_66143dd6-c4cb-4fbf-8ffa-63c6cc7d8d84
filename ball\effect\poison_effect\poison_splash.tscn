[gd_scene load_steps=23 format=3 uid="uid://udslrke1a8oy"]

[ext_resource type="Texture2D" uid="uid://donaevmmjeptc" path="res://assets/imgs/balls/普通球.png" id="1_irbes"]
[ext_resource type="Shader" uid="uid://b7lnuflpbunq7" path="res://assets/materials/shader/fluid.gdshader" id="2_hwapb"]
[ext_resource type="Script" uid="uid://clhvxb1l16y2r" path="res://ball/effect/poison_effect/poison_splash.gd" id="2_tbuqc"]
[ext_resource type="Shader" uid="uid://bionbfobjqg6w" path="res://assets/materials/shader/radiation_ball.gdshader" id="2_xj3vj"]
[ext_resource type="Script" uid="uid://c4hm0ggsbq3bl" path="res://addons/trail/trail.gd" id="3_qdg5e"]
[ext_resource type="Script" uid="uid://cmlkbo0gyw2yj" path="res://ball/ability/poison_ability.gd" id="4_qdg5e"]
[ext_resource type="Resource" uid="uid://bnekhwwjngk00" path="res://ball/effect/poison_effect/poison_config.tres" id="5_tbuqc"]

[sub_resource type="ShaderMaterial" id="ShaderMaterial_xj3vj"]
resource_local_to_scene = true
shader = ExtResource("2_xj3vj")
shader_parameter/time_scale = 6.0
shader_parameter/time_offset = 0.0
shader_parameter/core_color = Color(0.244964, 0.637949, 1.15514e-06, 1)
shader_parameter/edge_color = Color(0.4995, 1, 0.09, 1)
shader_parameter/size_scale = 1.5
shader_parameter/edge_softness = 0.0
shader_parameter/core_size = 1.369
shader_parameter/edge_size = 0.0
shader_parameter/noise_scale = 9.238
shader_parameter/noise_strength = 0.225

[sub_resource type="CanvasItemMaterial" id="CanvasItemMaterial_4ov6b"]
blend_mode = 1

[sub_resource type="Curve" id="Curve_rxth0"]
_data = [Vector2(0.76361, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_cyrwm"]
curve = SubResource("Curve_rxth0")

[sub_resource type="Curve" id="Curve_sa8la"]
_data = [Vector2(0, 0.781921), 0.0, 0.0, 0, 0, Vector2(0.647564, 0.636013), -0.628669, -0.628669, 0, 0, Vector2(1, 0.000603437), 0.0, 0.0, 0, 0]
point_count = 3

[sub_resource type="CurveTexture" id="CurveTexture_hvdpe"]
curve = SubResource("Curve_sa8la")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_2hpvm"]
lifetime_randomness = 0.45
particle_flag_disable_z = true
emission_shape = 1
emission_sphere_radius = 30.0
direction = Vector3(0, 1, 0)
spread = 10.0
initial_velocity_min = -100.0
initial_velocity_max = 100.0
gravity = Vector3(0, 200, 0)
damping_min = 50.0
damping_max = 80.0
scale_min = 0.02
scale_max = 0.04
scale_curve = SubResource("CurveTexture_hvdpe")
color = Color(0.166667, 1, 0, 1)
alpha_curve = SubResource("CurveTexture_cyrwm")

[sub_resource type="FastNoiseLite" id="FastNoiseLite_bjar0"]
resource_local_to_scene = true
noise_type = 4
seed = 210
frequency = 0.001
fractal_type = 3
fractal_gain = 1.235

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_fghg3"]
resource_local_to_scene = true
seamless = true
seamless_blend_skirt = 0.183
noise = SubResource("FastNoiseLite_bjar0")

[sub_resource type="FastNoiseLite" id="FastNoiseLite_x6eey"]
seed = 1095
frequency = 0.0003

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_5ktnd"]
resource_local_to_scene = true
seamless = true
seamless_blend_skirt = 0.283
noise = SubResource("FastNoiseLite_x6eey")

[sub_resource type="Gradient" id="Gradient_liiu0"]
offsets = PackedFloat32Array(0, 0.104015, 0.283333, 0.406934, 0.587591, 1)
colors = PackedColorArray(0.0105, 0.21, 0.020475, 0.909804, 0.2632, 0.94, 0.188, 0.909804, 0.144, 0.96, 0.3752, 0.0235294, 0.0255, 0.51, 0.187, 0.701961, 0, 0.803666, 0.172706, 0.898039, 0.498039, 1, 0.0901961, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_pa427"]
gradient = SubResource("Gradient_liiu0")
use_hdr = true

[sub_resource type="ShaderMaterial" id="ShaderMaterial_d4tbe"]
resource_local_to_scene = true
shader = ExtResource("2_hwapb")
shader_parameter/noise1 = SubResource("NoiseTexture2D_fghg3")
shader_parameter/noise2 = SubResource("NoiseTexture2D_5ktnd")
shader_parameter/scroll1 = Vector2(0.15, 0.25)
shader_parameter/scroll2 = Vector2(-0.15, -0.25)
shader_parameter/tex2_scale = 1.0
shader_parameter/overlap_color_gradient = SubResource("GradientTexture1D_pa427")
shader_parameter/overlap_factor = 1.2
shader_parameter/color_factor = 1.0
shader_parameter/blur = 1.0
shader_parameter/delay_v = 0.5
shader_parameter/delay_type = 0
shader_parameter/embed = true
shader_parameter/edge_threshold = 0.23
shader_parameter/edge_softness = 0.13
shader_parameter/edge_noise_scale = 1.8
shader_parameter/edge_noise_influence = 1.0
shader_parameter/edge_noise_scroll = Vector2(0.05, 0.03)
shader_parameter/edge_direction_mode = 0
shader_parameter/use_multiple_edges = true
shader_parameter/edge_left = true
shader_parameter/edge_right = false
shader_parameter/edge_top = true
shader_parameter/edge_bottom = true
shader_parameter/edge_radial = false
shader_parameter/edge_animation_speed = 10.0

[sub_resource type="Curve" id="Curve_pglyp"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(1, 1), 0.0, 0.0, 0, 0]
point_count = 2

[node name="PoisonSplash" type="Node2D"]
script = ExtResource("2_tbuqc")

[node name="Sprite2D" type="Sprite2D" parent="."]
modulate = Color(1.3, 1.3, 1.3, 1)
material = SubResource("ShaderMaterial_xj3vj")
scale = Vector2(0.12, 0.12)
texture = ExtResource("1_irbes")

[node name="GPUParticles2D" type="GPUParticles2D" parent="."]
material = SubResource("CanvasItemMaterial_4ov6b")
amount = 100
texture = ExtResource("1_irbes")
process_material = SubResource("ParticleProcessMaterial_2hpvm")

[node name="Trail" type="Line2D" parent="."]
top_level = true
z_index = -1
texture_repeat = 2
material = SubResource("ShaderMaterial_d4tbe")
width = 5.0
width_curve = SubResource("Curve_pglyp")
texture_mode = 2
joint_mode = 2
begin_cap_mode = 2
end_cap_mode = 2
script = ExtResource("3_qdg5e")
max_points = 60

[node name="PoisonAbility" type="Node" parent="."]
script = ExtResource("4_qdg5e")
debuff_template = ExtResource("5_tbuqc")
metadata/_custom_type_script = "uid://cmlkbo0gyw2yj"
