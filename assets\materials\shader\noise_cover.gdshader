shader_type canvas_item;

uniform sampler2D noise_pattern;
uniform sampler2D noise_pattern2;
uniform vec2 scroll = vec2(0,0.2);
uniform vec2 scrol2 = vec2(0,0.5);
uniform sampler2D overlap_color_gradient;
uniform float color_intensity : hint_range(0.0, 5.0) = 2.0;
instance uniform vec2 noise_seed = vec2(0.0, 0.0); // 每个实例的噪声种子
uniform float threshold : hint_range(0.0, 1.0) = 0.2; // 控制透明度阈值
uniform float noise_alpha : hint_range(0.0, 1.0) = 1.0; // 控制噪声透明度

void fragment() {
    // Get the original texture color
    vec4 original = texture(TEXTURE, UV);

    // 使用实例种子和时间缩放来创建独特的UV偏移
    vec2 seed_offset = noise_seed * 0.1;

    // 使用fract函数确保UV坐标保持在0-1范围内，实现无限循环滚动
    vec2 noise_uv = fract(UV + seed_offset + TIME * scroll);
    vec2 noise_uv2 = fract(UV - seed_offset + TIME * scrol2);

    // Sample the noise pattern with scrolling coordinates
    vec4 noise1 = texture(noise_pattern, noise_uv);
    vec4 noise2 = texture(noise_pattern2, noise_uv2);

    // 计算最终噪声效果
    vec4 final_noise = noise1 * noise2;

    // 使用噪声的亮度值来采样颜色渐变纹理
    // 这里我们使用噪声的红色通道作为颜色查找的索引
    float noise_brightness = final_noise.r;
    vec4 noise_color = texture(overlap_color_gradient, vec2(noise_brightness, 0.5));

    // 将颜色应用到噪声上
    vec4 colored_noise = final_noise * noise_color * color_intensity;

    // 使用噪声亮度来控制透明度
    // 低于阈值的部分将变为透明
    float fire_alpha = smoothstep(threshold, threshold + 0.1, noise_brightness) * noise_alpha;

    // 最终颜色：保留原始纹理，并在上面叠加带透明度的火焰效果
    // 使用混合模式将火焰叠加在原始图像上
    vec3 blended_color = mix(original.rgb, colored_noise.rgb, fire_alpha);

    // 保持原始图像的alpha不变
    COLOR = vec4(blended_color, original.a);
}
