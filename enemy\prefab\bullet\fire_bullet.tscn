[gd_scene load_steps=15 format=3 uid="uid://bw8xk7y2n8qfj"]

[ext_resource type="Script" uid="uid://blobhr08xu0wb" path="res://enemy/enemy_bullet.gd" id="1_bullet"]
[ext_resource type="Script" uid="uid://3lqxnj5nr7f1" path="res://addons/attribute_manager/AttributeComponent.gd" id="2_attr"]
[ext_resource type="Script" uid="uid://coux00joxi7h1" path="res://addons/attribute_manager/Attribute.gd" id="3_bitvt"]
[ext_resource type="Script" uid="uid://ucf5nur2irtk" path="res://attribute/scored_attribute.gd" id="4_3hnb2"]
[ext_resource type="Script" uid="uid://2dxckbgqoga5" path="res://addons/attribute_manager/AttributeSet.gd" id="5_m7c1g"]
[ext_resource type="Shader" uid="uid://bionbfobjqg6w" path="res://assets/materials/shader/radiation_ball.gdshader" id="6_3hnb2"]
[ext_resource type="Texture2D" uid="uid://donaevmmjeptc" path="res://assets/imgs/balls/普通球.png" id="7_m7c1g"]

[sub_resource type="Resource" id="Resource_vs7eg"]
script = ExtResource("4_3hnb2")
growth_per_score = 0.01
operate_type = 0
max_value = 9999.0
min_value = -9999.0
base_value = 5.0
can_cache = false
metadata/_custom_type_script = "uid://ucf5nur2irtk"

[sub_resource type="Resource" id="Resource_70jgv"]
script = ExtResource("3_bitvt")
base_value = 0.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_3hnb2"]
script = ExtResource("4_3hnb2")
growth_per_score = 0.01
operate_type = 0
max_value = 9999.0
min_value = -9999.0
base_value = 10.0
can_cache = false
metadata/_custom_type_script = "uid://ucf5nur2irtk"

[sub_resource type="Resource" id="Resource_ywh8d"]
script = ExtResource("4_3hnb2")
growth_per_score = 0.001
operate_type = 0
max_value = 9999.0
min_value = -9999.0
base_value = 50.0
can_cache = false
metadata/_custom_type_script = "uid://ucf5nur2irtk"

[sub_resource type="Resource" id="Resource_g00ju"]
script = ExtResource("5_m7c1g")
attributes = Dictionary[StringName, ExtResource("3_bitvt")]({
&"damage": SubResource("Resource_vs7eg"),
&"hp": SubResource("Resource_70jgv"),
&"max_hp": SubResource("Resource_3hnb2"),
&"speed": SubResource("Resource_ywh8d")
})
metadata/_custom_type_script = "uid://2dxckbgqoga5"

[sub_resource type="ShaderMaterial" id="ShaderMaterial_vs7eg"]
shader = ExtResource("6_3hnb2")
shader_parameter/time_scale = 3.295
shader_parameter/time_offset = 0.0
shader_parameter/core_color = Color(0.761671, 0.665957, 0, 1)
shader_parameter/edge_color = Color(0.737255, 0.215686, 0, 1)
shader_parameter/size_scale = 1.4
shader_parameter/edge_softness = 0.0
shader_parameter/core_size = 2.0
shader_parameter/edge_size = 0.0
shader_parameter/noise_scale = 2.5
shader_parameter/noise_strength = 0.825

[sub_resource type="CircleShape2D" id="CircleShape2D_bullet"]
radius = 19.0263

[node name="FireBullet" type="Area2D"]
scale = Vector2(0.4, 0.4)
collision_layer = 64
collision_mask = 50
script = ExtResource("1_bullet")

[node name="AttributeComponent" type="Node" parent="."]
script = ExtResource("2_attr")
attribute_set = SubResource("Resource_g00ju")

[node name="Sprite2D" type="Sprite2D" parent="."]
material = SubResource("ShaderMaterial_vs7eg")
scale = Vector2(0.5, 0.5)
texture = ExtResource("7_m7c1g")

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
shape = SubResource("CircleShape2D_bullet")

[connection signal="area_entered" from="." to="." method="_on_area_entered"]
[connection signal="body_entered" from="." to="." method="_on_body_entered"]
