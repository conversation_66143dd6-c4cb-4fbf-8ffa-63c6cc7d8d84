class_name GameConstants
extends RefCounted

## 游戏全局常量类
##
## 定义游戏中使用的所有全局常量，包括稀有度系统、物品类型等
## 避免在各个类中重复定义，确保数据一致性

const MAX_LEVEL: int = 3

## 弹球类型枚举
enum BallType {
	NORMAL, ## 普通弹球
	BASE, ## 基础弹球
	FUSION, ## 融合弹球
	EVOLUTION, ## 进化弹球
	MIX, ## 进化融合弹球
}

## 已拥有物品权重提高倍数
const OWNED_ITEM_WEIGHT_MULTIPLIER: float = 2.0

## 稀有度等级枚举
enum RarityLevel {
	COMMON, ## 普通
	RARE, ## 稀有
	EPIC, ## 史诗
	LEGENDARY ## 传说
}

## 物品类型枚举
enum ItemType {
	BALL, ## 弹球
	RELIC ## 遗物
}

## 升级选项类型枚举
enum UpgradeType {
	NEW_BALL, ## 新弹球
	UPGRADE_BALL, ## 弹球升级
	NEW_RELIC, ## 新遗物
	UPGRADE_RELIC ## 遗物升级
}

## 稀有度等级字典（枚举值 -> 中文显示名称）
const RARITY_NAMES: Dictionary = {
	RarityLevel.COMMON: "普通",
	RarityLevel.RARE: "稀有",
	RarityLevel.EPIC: "史诗",
	RarityLevel.LEGENDARY: "传说"
}

## 稀有度等级字典（中文显示名称 -> 枚举值）
const RARITY_KEYS: Dictionary = {
	"普通": RarityLevel.COMMON,
	"稀有": RarityLevel.RARE,
	"史诗": RarityLevel.EPIC,
	"传说": RarityLevel.LEGENDARY
}


## 物品类型字典（枚举值 -> 字符串）
const ITEM_TYPE_NAMES: Dictionary = {
	ItemType.BALL: "ball",
	ItemType.RELIC: "relic"
}

## 物品类型字典（字符串 -> 枚举值）
const ITEM_TYPE_KEYS: Dictionary = {
	"ball": ItemType.BALL,
	"relic": ItemType.RELIC
}

## 升级类型字典（枚举值 -> 字符串）
const UPGRADE_TYPE_NAMES: Dictionary = {
	UpgradeType.NEW_BALL: "new_ball",
	UpgradeType.UPGRADE_BALL: "upgrade_ball",
	UpgradeType.NEW_RELIC: "new_relic",
	UpgradeType.UPGRADE_RELIC: "upgrade_relic"
}


## 获取稀有度的中文显示名称
## @param rarity: 稀有度等级枚举值
## @return: 中文显示名称
static func get_rarity_display_name(rarity: RarityLevel) -> String:
	return RARITY_NAMES.get(rarity, "未知")


## 从中文名称获取稀有度等级
## @param display_name: 中文显示名称
## @return: 稀有度等级枚举值
static func get_rarity_from_display_name(display_name: String) -> RarityLevel:
	return RARITY_KEYS.get(display_name, RarityLevel.COMMON)


## 获取稀有度对应的颜色
## @param rarity: 稀有度等级枚举值
## @return: 对应的颜色
static func get_rarity_color(rarity: RarityLevel) -> Color:
	match rarity:
		RarityLevel.COMMON:
			return Color.WHITE
		RarityLevel.RARE:
			return Color.GREEN
		RarityLevel.EPIC:
			return Color.BLUE
		RarityLevel.LEGENDARY:
			return Color.PURPLE
		_:
			return Color.GRAY

## 获取物品类型的字符串表示
## @param item_type: 物品类型枚举值
## @return: 字符串表示
static func get_item_type_name(item_type: ItemType) -> String:
	return ITEM_TYPE_NAMES.get(item_type, "unknown")

## 从字符串获取物品类型
## @param type_name: 物品类型字符串
## @return: 物品类型枚举值
static func get_item_type_from_name(type_name: String) -> ItemType:
	return ITEM_TYPE_KEYS.get(type_name, ItemType.BALL)

## 验证物品类型是否有效
## @param item_type: 物品类型枚举值
## @return: 是否为有效的物品类型
static func is_valid_item_type(item_type: ItemType) -> bool:
	return item_type in ItemType.values()

## 获取升级类型的字符串表示
## @param upgrade_type: 升级类型枚举值
## @return: 字符串表示
static func get_upgrade_type_name(upgrade_type: UpgradeType) -> String:
	return UPGRADE_TYPE_NAMES.get(upgrade_type, "unknown")


## 获取所有稀有度等级
## @return: 稀有度等级数组
static func get_all_rarities() -> Array[RarityLevel]:
	var rarities: Array[RarityLevel] = []
	for rarity in RarityLevel.values():
		rarities.append(rarity)
	return rarities


## 比较两个稀有度等级的高低
## @param rarity1: 第一个稀有度
## @param rarity2: 第二个稀有度
## @return: 如果rarity1更稀有返回1，相等返回0，否则返回-1
static func compare_rarity(rarity1: RarityLevel, rarity2: RarityLevel) -> int:
	if rarity1 > rarity2:
		return 1
	elif rarity1 < rarity2:
		return -1
	else:
		return 0
