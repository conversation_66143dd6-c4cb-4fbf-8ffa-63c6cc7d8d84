[gd_scene load_steps=41 format=3 uid="uid://bgmy06ct41lfi"]

[ext_resource type="PhysicsMaterial" uid="uid://d0d4n7iiiu1o8" path="res://assets/materials/physics/ball.tres" id="1_dg4t1"]
[ext_resource type="Script" uid="uid://drjqkf3mt3gs1" path="res://ball/ball_base.gd" id="2_684hn"]
[ext_resource type="Script" uid="uid://3lqxnj5nr7f1" path="res://addons/attribute_manager/AttributeComponent.gd" id="3_6ouho"]
[ext_resource type="Material" uid="uid://bsdji8rh2c8r5" path="res://ball/effect/ball_sprite_material/poison_ball.tres" id="3_684hn"]
[ext_resource type="Script" uid="uid://boltc4gkngyox" path="res://ui/res/ball_ui_resource.gd" id="3_ngan6"]
[ext_resource type="Script" uid="uid://c4hm0ggsbq3bl" path="res://addons/trail/trail.gd" id="4_f8vjm"]
[ext_resource type="Script" uid="uid://coux00joxi7h1" path="res://addons/attribute_manager/Attribute.gd" id="4_pa427"]
[ext_resource type="Texture2D" uid="uid://u5ndsxu34wr3" path="res://assets/imgs/balls/128x128.png" id="4_pwu0t"]
[ext_resource type="Script" uid="uid://5ww2mn76prh4" path="res://attribute/leveled_attribute.gd" id="5_7irxg"]
[ext_resource type="Shader" uid="uid://b7lnuflpbunq7" path="res://assets/materials/shader/fluid.gdshader" id="5_f8vjm"]
[ext_resource type="Script" uid="uid://2dxckbgqoga5" path="res://addons/attribute_manager/AttributeSet.gd" id="6_5hu3v"]
[ext_resource type="Texture2D" uid="uid://vwfv2n316yx6" path="res://assets/imgs/effects/46.png" id="7_f8vjm"]
[ext_resource type="Script" uid="uid://qu5oshh0ccl5" path="res://attribute/ball_damage_attribute.gd" id="8_4iknv"]
[ext_resource type="Script" uid="uid://cmlkbo0gyw2yj" path="res://ball/ability/poison_ability.gd" id="8_883au"]
[ext_resource type="Resource" uid="uid://bnekhwwjngk00" path="res://ball/effect/poison_effect/poison_config.tres" id="9_fghg3"]
[ext_resource type="Script" uid="uid://b14ywpmhnqjgo" path="res://ui/res/ability_ui_resource.gd" id="15_j8mjf"]

[sub_resource type="Resource" id="Resource_j8mjf"]
script = ExtResource("3_ngan6")
ball_name = "剧毒"
description = "命中伤害 [color=#7fff17][min_damage]-[max_damage][/color]"
description_level_up = "命中伤害 [color=#7fff17][min_damage]-[max_damage][/color] > [color=#7fff17][min_damage+1]-[max_damage+1][/color]"
icon_type = 2
icon_text = ""
icon_shader_material = ExtResource("3_684hn")
rarity_level = 0
rarity_weight = 10.0
metadata/_custom_type_script = "uid://boltc4gkngyox"

[sub_resource type="Resource" id="Resource_a657q"]
script = ExtResource("4_pa427")
base_value = 5.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_usc81"]
script = ExtResource("8_4iknv")
min_value_source = "min_damage"
max_value_source = "max_damage"
crit_rate_source = "crit"
base_value = 0.0
can_cache = true
metadata/_custom_type_script = "uid://qu5oshh0ccl5"

[sub_resource type="Resource" id="Resource_7410v"]
script = ExtResource("4_pa427")
base_value = 1.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_jae14"]
script = ExtResource("5_7irxg")
growth_per_level = 6.0
base_value = 5.0
can_cache = true
metadata/_custom_type_script = "uid://5ww2mn76prh4"

[sub_resource type="Resource" id="Resource_jhcrr"]
script = ExtResource("5_7irxg")
growth_per_level = 2.0
base_value = 3.0
can_cache = true
metadata/_custom_type_script = "uid://5ww2mn76prh4"

[sub_resource type="Resource" id="Resource_6ouho"]
script = ExtResource("4_pa427")
base_value = 500.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_mbwrm"]
resource_local_to_scene = true
script = ExtResource("6_5hu3v")
attributes = Dictionary[StringName, ExtResource("4_pa427")]({
&"crit": SubResource("Resource_a657q"),
&"damage": SubResource("Resource_usc81"),
&"level": SubResource("Resource_7410v"),
&"max_damage": SubResource("Resource_jae14"),
&"min_damage": SubResource("Resource_jhcrr"),
&"speed": SubResource("Resource_6ouho")
})
metadata/_custom_type_script = "uid://2dxckbgqoga5"

[sub_resource type="CircleShape2D" id="CircleShape2D_yi17e"]
radius = 13.0

[sub_resource type="FastNoiseLite" id="FastNoiseLite_883au"]
resource_local_to_scene = true
noise_type = 4
seed = 210
frequency = 0.001
fractal_type = 3
fractal_gain = 1.235

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_fghg3"]
resource_local_to_scene = true
seamless = true
seamless_blend_skirt = 0.183
noise = SubResource("FastNoiseLite_883au")

[sub_resource type="FastNoiseLite" id="FastNoiseLite_6dy7x"]
seed = 1095
frequency = 0.0003

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_5ktnd"]
resource_local_to_scene = true
seamless = true
seamless_blend_skirt = 0.283
noise = SubResource("FastNoiseLite_6dy7x")

[sub_resource type="Gradient" id="Gradient_6ouho"]
offsets = PackedFloat32Array(0, 0.104015, 0.283333, 0.406934, 0.587591, 1)
colors = PackedColorArray(0.0105, 0.21, 0.020475, 0.909804, 0.2632, 0.94, 0.188, 0.909804, 0.144, 0.96, 0.3752, 0.0235294, 0.0255, 0.51, 0.187, 0.701961, 0, 0.803666, 0.172706, 0.898039, 0.498039, 1, 0.0901961, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_pa427"]
gradient = SubResource("Gradient_6ouho")
use_hdr = true

[sub_resource type="ShaderMaterial" id="ShaderMaterial_7irxg"]
resource_local_to_scene = true
shader = ExtResource("5_f8vjm")
shader_parameter/noise1 = SubResource("NoiseTexture2D_fghg3")
shader_parameter/noise2 = SubResource("NoiseTexture2D_5ktnd")
shader_parameter/scroll1 = Vector2(0.15, 0.25)
shader_parameter/scroll2 = Vector2(-0.15, -0.25)
shader_parameter/tex2_scale = 1.0
shader_parameter/overlap_color_gradient = SubResource("GradientTexture1D_pa427")
shader_parameter/overlap_factor = 1.2
shader_parameter/color_factor = 1.0
shader_parameter/blur = 1.0
shader_parameter/delay_v = 0.5
shader_parameter/delay_type = 0
shader_parameter/embed = true
shader_parameter/edge_threshold = 0.23
shader_parameter/edge_softness = 0.13
shader_parameter/edge_noise_scale = 1.8
shader_parameter/edge_noise_influence = 1.0
shader_parameter/edge_noise_scroll = Vector2(0.05, 0.03)
shader_parameter/edge_direction_mode = 0
shader_parameter/use_multiple_edges = true
shader_parameter/edge_left = true
shader_parameter/edge_right = false
shader_parameter/edge_top = true
shader_parameter/edge_bottom = true
shader_parameter/edge_radial = false
shader_parameter/edge_animation_speed = 10.0

[sub_resource type="Curve" id="Curve_yi17e"]
_data = [Vector2(0, 0.248689), 0.0, 0.853875, 0, 0, Vector2(1, 1), 0.0480682, 0.0, 0, 0]
point_count = 2

[sub_resource type="CanvasItemMaterial" id="CanvasItemMaterial_5hu3v"]
blend_mode = 1

[sub_resource type="Curve" id="Curve_2nx2w"]
_data = [Vector2(0, 0), 0.0, 0.0, 0, 0, Vector2(0.32699, 0), 0.0, 0.0, 0, 0, Vector2(0.50173, 1), 0.0, 0.0, 0, 0, Vector2(0.792387, 1), 0.0, 0.0, 0, 0, Vector2(1, 0), 0.0, 0.0, 0, 0]
point_count = 5

[sub_resource type="CurveTexture" id="CurveTexture_883au"]
curve = SubResource("Curve_2nx2w")

[sub_resource type="Curve" id="Curve_fghg3"]
_data = [Vector2(0.50173, 0.834174), 0.0, 0.0, 0, 0, Vector2(1, 0.673815), 0.0, 0.0, 0, 0]
point_count = 2

[sub_resource type="CurveTexture" id="CurveTexture_fghg3"]
curve = SubResource("Curve_fghg3")

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_5ktnd"]
lifetime_randomness = 0.45
particle_flag_disable_z = true
emission_shape = 1
emission_sphere_radius = 35.0
gravity = Vector3(0, -28.885, 0)
scale_min = 0.01
scale_max = 0.04
scale_curve = SubResource("CurveTexture_fghg3")
color = Color(0.266667, 1, 0, 1)
alpha_curve = SubResource("CurveTexture_883au")
turbulence_enabled = true
turbulence_noise_strength = 2.43
turbulence_noise_scale = 10.0
turbulence_noise_speed = Vector3(0, 1.79, 0)
turbulence_noise_speed_random = 1.1
turbulence_influence_min = 0.022

[sub_resource type="Resource" id="Resource_4iknv"]
script = ExtResource("15_j8mjf")
description = "对击中敌人施加[color=#7fff17]1[/color]层中毒状态。中毒效果持续[color=#7fff17][duration][/color]秒，每层中毒每[color=#7fff17][period][/color]秒造成[color=#7fff17][value][/color]伤害，最多叠加[color=#7fff17][max_stacks][/color]层"
description_level_up = "对击中敌人施加[color=#7fff17]1[/color]层中毒状态。中毒效果持续[color=#7fff17][duration][/color]秒，每层中毒每[color=#7fff17][period][/color]秒造成[color=#7fff17][value][/color] > [color=#7fff17][value+1][/color]伤害，最多叠加[color=#7fff17][max_stacks][/color]层"
metadata/_custom_type_script = "uid://b14ywpmhnqjgo"

[node name="PoisonBall" type="RigidBody2D"]
collision_layer = 2
collision_mask = 13
physics_material_override = ExtResource("1_dg4t1")
gravity_scale = 0.0
can_sleep = false
lock_rotation = true
custom_integrator = true
continuous_cd = 1
linear_damp_mode = 1
angular_damp_mode = 1
script = ExtResource("2_684hn")
ui_resource = SubResource("Resource_j8mjf")
ball_type = 1
cooldown = 15.0
acceleration_multiplier = 1.01
damage_color = Color(0.498039, 1, 0.0901961, 1)

[node name="AttributeComponent" type="Node" parent="."]
script = ExtResource("3_6ouho")
attribute_set = SubResource("Resource_mbwrm")
metadata/_custom_type_script = "uid://3lqxnj5nr7f1"

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
shape = SubResource("CircleShape2D_yi17e")

[node name="Sprite2D" type="Sprite2D" parent="."]
material = ExtResource("3_684hn")
scale = Vector2(0.4, 0.4)
texture = ExtResource("4_pwu0t")

[node name="Trail" type="Line2D" parent="Sprite2D"]
top_level = true
z_index = -1
texture_repeat = 2
material = SubResource("ShaderMaterial_7irxg")
width = 30.0
width_curve = SubResource("Curve_yi17e")
texture_mode = 2
joint_mode = 2
begin_cap_mode = 2
end_cap_mode = 2
script = ExtResource("4_f8vjm")

[node name="GPUParticles2D" type="GPUParticles2D" parent="Sprite2D"]
material = SubResource("CanvasItemMaterial_5hu3v")
scale = Vector2(2.5, 2.5)
amount = 10
texture = ExtResource("7_f8vjm")
lifetime = 0.6
process_material = SubResource("ParticleProcessMaterial_5ktnd")

[node name="Abilities" type="Node" parent="."]

[node name="PoisonAbility" type="Node" parent="Abilities"]
script = ExtResource("8_883au")
debuff_template = ExtResource("9_fghg3")
ui_resource = SubResource("Resource_4iknv")
metadata/_custom_type_script = "uid://cmlkbo0gyw2yj"
