[gd_resource type="VisualShader" load_steps=5 format=3 uid="uid://brjwypq2onoc5"]

[sub_resource type="VisualShaderNodeColorParameter" id="VisualShaderNodeColorParameter_cdvur"]
parameter_name = "hit_color"
qualifier = 2
default_value_enabled = true

[sub_resource type="VisualShaderNodeInput" id="VisualShaderNodeInput_cdvur"]
input_name = "color"

[sub_resource type="VisualShaderNodeFloatParameter" id="VisualShaderNodeFloatParameter_cdvur"]
parameter_name = "rate"
default_value_enabled = true

[sub_resource type="VisualShaderNodeMix" id="VisualShaderNodeMix_cdvur"]
default_input_values = [0, Quaternion(0, 0, 0, 0), 1, Quaternion(1, 1, 1, 1), 2, Quaternion(0.5, 0.5, 0.5, 0.5)]
op_type = 5

[resource]
code = "shader_type canvas_item;
render_mode blend_mix;

instance uniform vec4 hit_color : source_color = vec4(1.000000, 1.000000, 1.000000, 1.000000);
uniform float rate = 0.0;



void fragment() {
// Input:3
	vec4 n_out3p0 = COLOR;


// ColorParameter:2
	vec4 n_out2p0 = hit_color;


// FloatParameter:6
	float n_out6p0 = rate;


// Mix:9
	vec4 n_out9p0 = mix(n_out3p0, n_out2p0, vec4(n_out6p0));


// Output:0
	COLOR.rgb = vec3(n_out9p0.xyz);


}
"
mode = 1
flags/light_only = false
nodes/fragment/2/node = SubResource("VisualShaderNodeColorParameter_cdvur")
nodes/fragment/2/position = Vector2(-980, 120)
nodes/fragment/3/node = SubResource("VisualShaderNodeInput_cdvur")
nodes/fragment/3/position = Vector2(-980, -120)
nodes/fragment/6/node = SubResource("VisualShaderNodeFloatParameter_cdvur")
nodes/fragment/6/position = Vector2(-800, -640)
nodes/fragment/9/node = SubResource("VisualShaderNodeMix_cdvur")
nodes/fragment/9/position = Vector2(-160, 60)
nodes/fragment/connections = PackedInt32Array(2, 0, 9, 1, 3, 0, 9, 0, 6, 0, 9, 2, 9, 0, 0, 0)
