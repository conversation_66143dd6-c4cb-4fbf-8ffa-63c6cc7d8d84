extends Node2D
class_name Main

@onready var camera: Camera2D = $Camera2D
@onready var player_manager: PlayerManager = $Player
@onready var enemy_spawn: EnemySpawn = $EnemySpawn
@onready var game_status: GameStatusPanel = $CanvasLayer/GameStatusPanel
@onready var damage_mesh_manager: DamageMeshManager = $Managers/DamageMeshManager
@onready var border_collision: CollisionPolygon2D = $Border/CollisionPolygon2D
@onready var upgrade_manager: UpgradeManager = $Managers/UpgradeManager
@onready var level_up_panel: LevelUpPanel = $CanvasLayer/LevelUpPanel
@onready var ball_pool_manager: BallPoolManager = $Managers/BallPoolManager


@export var ball_scenes: Array[PackedScene] = []
@export var relic_resources: Array[Relic] = []

@export var damage_number_offset: Vector2 = Vector2(0, -20)

var boss_appear: bool = false
var recent_damages: Array[int] = []


func _ready():
	print("game loaded")
	update_border()
	game_status.init_bar(player_manager.hp, player_manager.max_hp)
	update_layout()
	get_viewport().size_changed.connect(update_layout)

	# 连接全局事件
	GameEvents.enemy_died.connect(_on_enemy_die)
	GameEvents.enemy_received_damage.connect(_on_enemy_damage_taken)
	GameEvents.enemy_attacked.connect(_on_enemy_attack)
	GameEvents.player_received_damage.connect(_on_player_damage_taken)

	# 设置升级管理器的依赖注入
	_setup_upgrade_manager_dependencies()

	# 设置玩家管理器的依赖注入
	_setup_player_manager_dependencies()



	# 设置敌人生成器的依赖注入
	_setup_enemy_spawn_dependencies()


## 设置升级管理器的依赖注入
func _setup_upgrade_manager_dependencies() -> void:
	# 获取遗物管理器引用
	var relic_manager: RelicManager = null
	if player_manager:
		relic_manager = player_manager.get_node_or_null("RelicManager")

	# 注入依赖
	upgrade_manager.set_dependencies(level_up_panel, player_manager, ball_pool_manager, \
	relic_manager, ball_scenes, relic_resources)


## 设置玩家管理器的依赖注入
func _setup_player_manager_dependencies() -> void:
	player_manager.set_ball_pool_manager(ball_pool_manager)





## 设置敌人生成器的依赖注入
func _setup_enemy_spawn_dependencies() -> void:
	enemy_spawn.ball_pool_manager = ball_pool_manager

func update_layout():
	var size = get_viewport_rect().size
	camera.position = size / 2


func _on_enemy_attack(enemy: EnemyBase):
	player_manager.take_damage(enemy.get_damage(), Color.RED, null)


func update_border():
	var size = get_viewport().get_visible_rect().size
	var w = size.x
	var h = size.y
	var t: float = 1000.0 # 厚度

	# 8个端点，顺时针排列，参考main.tscn
	var poly_points: Array[Variant] = [
		Vector2(0, 0), # 左上外
		Vector2(0, h), # 左下外
		Vector2(-t, h), # 左下内
		Vector2(-t, -t), # 左上内
		Vector2(w + t, -t), # 右上内
		Vector2(w + t, h), # 右下内
		Vector2(w, h), # 右下外
		Vector2(w, 0), # 右上外
	]

	var polygon = PackedVector2Array(poly_points)
	border_collision.polygon = polygon


func _on_enemy_die(enemy: EnemyBase, _killer: Object) -> void:
	GameManager.add_score(enemy.score)
	GameManager.add_exp(enemy.experience)


func _on_enemy_damage_taken(enemy: EnemyBase, damage_info: Dictionary) -> void:
	var amount: int = damage_info["amount"]
	var color: Color = damage_info["color"]
	var is_crit: bool = damage_info["crit"]
	# 记录伤害值，保持最多1000条
	recent_damages.append(amount)
	if recent_damages.size() > 1000:
		recent_damages.pop_front()

	# 根据伤害在最近记录中的相对大小计算缩放系数
	var show_scale := 1.0
	if recent_damages.size() > 1:
		var min_damage: int = recent_damages[0]
		var max_damage: int = recent_damages[0]
		for dmg in recent_damages:
			if dmg < min_damage:
				min_damage = dmg
			elif dmg > max_damage:
				max_damage = dmg
		if min_damage != max_damage:
			var ratio: float = (amount - min_damage) / float(max_damage - min_damage) # 0~1

			# 根据记录次数计算缩放范围
			var max_records := 1000
			var records_ratio: float = min(recent_damages.size(), max_records) / float(max_records)
			var scale_range: float = 0.3 * records_ratio # 最大偏差为0.3

			# 计算最终缩放系数，范围从1.0-scale_range到1.0+scale_range
			show_scale = 1.0 + scale_range * (2.0 * ratio - 1.0)
		else:
			show_scale = 1.0 # 所有伤害相同，使用默认缩放
	damage_mesh_manager.spawn_damage_number(enemy._get_random_position_on_sprite() + damage_number_offset, \
	str(amount) + ("!" if is_crit else ""), \
	Color.YELLOW if is_crit and color == Color.WHITE else color, show_scale)


func _on_player_damage_taken(player: PlayerManager, damage_info: Dictionary) -> void:
	var amount: int = damage_info["damage"]
	damage_mesh_manager.spawn_damage_number(player.global_position + damage_number_offset, \
	str(amount), Color.RED, 1.0)
