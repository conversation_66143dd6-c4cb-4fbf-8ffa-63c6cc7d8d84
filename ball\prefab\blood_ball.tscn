[gd_scene load_steps=34 format=3 uid="uid://bh7jabuqr1upf"]

[ext_resource type="PhysicsMaterial" uid="uid://d0d4n7iiiu1o8" path="res://assets/materials/physics/ball.tres" id="1_0kw5n"]
[ext_resource type="Script" uid="uid://drjqkf3mt3gs1" path="res://ball/ball_base.gd" id="2_j34a3"]
[ext_resource type="Script" uid="uid://boltc4gkngyox" path="res://ui/res/ball_ui_resource.gd" id="3_7cena"]
[ext_resource type="Script" uid="uid://3lqxnj5nr7f1" path="res://addons/attribute_manager/AttributeComponent.gd" id="3_7vflf"]
[ext_resource type="Texture2D" uid="uid://donaevmmjeptc" path="res://assets/imgs/balls/普通球.png" id="3_mwx5r"]
[ext_resource type="Material" uid="uid://yuj6qn0q5oh5" path="res://ball/effect/ball_sprite_material/blood_ball.tres" id="3_v1ct7"]
[ext_resource type="Script" uid="uid://coux00joxi7h1" path="res://addons/attribute_manager/Attribute.gd" id="4_ck2q6"]
[ext_resource type="Script" uid="uid://c4hm0ggsbq3bl" path="res://addons/trail/trail.gd" id="4_v1ct7"]
[ext_resource type="Shader" uid="uid://b7lnuflpbunq7" path="res://assets/materials/shader/fluid.gdshader" id="5_u0ia3"]
[ext_resource type="Script" uid="uid://5ww2mn76prh4" path="res://attribute/leveled_attribute.gd" id="6_5gc88"]
[ext_resource type="Script" uid="uid://pe82fwtsrv88" path="res://ball/ability/blood_ability.gd" id="7_u0ia3"]
[ext_resource type="Script" uid="uid://qu5oshh0ccl5" path="res://attribute/ball_damage_attribute.gd" id="8_f3wq4"]
[ext_resource type="Script" uid="uid://2dxckbgqoga5" path="res://addons/attribute_manager/AttributeSet.gd" id="8_htjri"]
[ext_resource type="Resource" uid="uid://gxio2wtbnt5o" path="res://ball/effect/blood_effect/blood_config.tres" id="8_xuec2"]
[ext_resource type="Script" uid="uid://b14ywpmhnqjgo" path="res://ui/res/ability_ui_resource.gd" id="14_f3wq4"]

[sub_resource type="Resource" id="Resource_fhoko"]
script = ExtResource("3_7cena")
ball_name = "尖刺"
description = "命中伤害 [color=#9c0000][min_damage]-[max_damage][/color]"
description_level_up = "命中伤害 [color=#9c0000][min_damage]-[max_damage][/color] > [color=#9c0000][min_damage+1]-[max_damage+1][/color]"
icon_type = 2
icon_text = ""
icon_shader_material = ExtResource("3_v1ct7")
rarity_level = 0
rarity_weight = 10.0
metadata/_custom_type_script = "uid://boltc4gkngyox"

[sub_resource type="Resource" id="Resource_udgyg"]
script = ExtResource("4_ck2q6")
base_value = 5.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_2by7n"]
script = ExtResource("8_f3wq4")
min_value_source = "min_damage"
max_value_source = "max_damage"
crit_rate_source = "crit"
base_value = 0.0
can_cache = true
metadata/_custom_type_script = "uid://qu5oshh0ccl5"

[sub_resource type="Resource" id="Resource_adf0b"]
script = ExtResource("4_ck2q6")
base_value = 1.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_sf1ll"]
script = ExtResource("6_5gc88")
growth_per_level = 12.0
base_value = 20.0
can_cache = true
metadata/_custom_type_script = "uid://5ww2mn76prh4"

[sub_resource type="Resource" id="Resource_wibsi"]
script = ExtResource("6_5gc88")
growth_per_level = 5.0
base_value = 10.0
can_cache = true
metadata/_custom_type_script = "uid://5ww2mn76prh4"

[sub_resource type="Resource" id="Resource_2s2wc"]
script = ExtResource("4_ck2q6")
base_value = 500.0
can_cache = true
metadata/_custom_type_script = "uid://coux00joxi7h1"

[sub_resource type="Resource" id="Resource_pti4m"]
resource_local_to_scene = true
script = ExtResource("8_htjri")
attributes = Dictionary[StringName, ExtResource("4_ck2q6")]({
&"crit": SubResource("Resource_udgyg"),
&"damage": SubResource("Resource_2by7n"),
&"level": SubResource("Resource_adf0b"),
&"max_damage": SubResource("Resource_sf1ll"),
&"min_damage": SubResource("Resource_wibsi"),
&"speed": SubResource("Resource_2s2wc")
})
metadata/_custom_type_script = "uid://2dxckbgqoga5"

[sub_resource type="FastNoiseLite" id="FastNoiseLite_xuec2"]
resource_local_to_scene = true
noise_type = 4
seed = 210
frequency = 0.001
fractal_type = 3
fractal_gain = 1.235

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_d1dsh"]
resource_local_to_scene = true
seamless = true
seamless_blend_skirt = 0.183
noise = SubResource("FastNoiseLite_xuec2")

[sub_resource type="FastNoiseLite" id="FastNoiseLite_ydjwq"]
seed = 1095
frequency = 0.0007

[sub_resource type="NoiseTexture2D" id="NoiseTexture2D_pti4m"]
resource_local_to_scene = true
seamless = true
seamless_blend_skirt = 0.283
noise = SubResource("FastNoiseLite_ydjwq")

[sub_resource type="Gradient" id="Gradient_2s2wc"]
offsets = PackedFloat32Array(0, 0.100299, 0.283333, 0.406934, 0.718563, 1)
colors = PackedColorArray(0.21, 0.0105, 0.0105, 0.584314, 0.94, 0.188, 0.200533, 0.909804, 0.96, 0.144, 0.144, 0.0235294, 0.423917, 0.0172182, 0.0226198, 0.701961, 0.565949, 0.000681363, 0.000372339, 1, 0.572196, 0, 0.0216751, 1)

[sub_resource type="GradientTexture1D" id="GradientTexture1D_7vflf"]
gradient = SubResource("Gradient_2s2wc")
use_hdr = true

[sub_resource type="ShaderMaterial" id="ShaderMaterial_ck2q6"]
resource_local_to_scene = true
shader = ExtResource("5_u0ia3")
shader_parameter/noise1 = SubResource("NoiseTexture2D_d1dsh")
shader_parameter/noise2 = SubResource("NoiseTexture2D_pti4m")
shader_parameter/scroll1 = Vector2(0.15, 0.25)
shader_parameter/scroll2 = Vector2(-0.15, -0.25)
shader_parameter/tex2_scale = 1.0
shader_parameter/overlap_color_gradient = SubResource("GradientTexture1D_7vflf")
shader_parameter/overlap_factor = 1.3
shader_parameter/color_factor = 1.5
shader_parameter/blur = 1.0
shader_parameter/delay_v = 0.4
shader_parameter/delay_type = 0
shader_parameter/embed = false
shader_parameter/edge_threshold = 0.23
shader_parameter/edge_softness = 0.1
shader_parameter/edge_noise_scale = 3.5
shader_parameter/edge_noise_influence = 0.5
shader_parameter/edge_noise_scroll = Vector2(0.05, 0.03)
shader_parameter/edge_direction_mode = 0
shader_parameter/use_multiple_edges = true
shader_parameter/edge_left = true
shader_parameter/edge_right = false
shader_parameter/edge_top = true
shader_parameter/edge_bottom = true
shader_parameter/edge_radial = false
shader_parameter/edge_animation_speed = 10.0

[sub_resource type="Curve" id="Curve_yi17e"]
_data = [Vector2(0, 0.248689), 0.0, 0.853875, 0, 0, Vector2(1, 1), 0.0480682, 0.0, 0, 0]
point_count = 2

[sub_resource type="CircleShape2D" id="CircleShape2D_yi17e"]
radius = 20.025

[sub_resource type="Resource" id="Resource_rw2wu"]
script = ExtResource("14_f3wq4")
description = "对击中敌人施加[color=#9c0000]1[/color]层流血状态。流血效果持续[color=#9c0000][duration][/color]秒，每层流血每[color=#9c0000][period][/color]秒造成[color=#9c0000][value][/color]伤害，最多叠加[color=#9c0000][max_stacks][/color]层"
description_level_up = "对击中敌人施加[color=#9c0000]1[/color]层流血状态。流血效果持续[color=#9c0000][duration][/color]秒，每层流血每[color=#9c0000][period][/color]秒造成[color=#9c0000][value][/color] > [color=#9c0000][value+1][/color]伤害，最多叠加[color=#9c0000][max_stacks][/color]层"
metadata/_custom_type_script = "uid://b14ywpmhnqjgo"

[node name="BloodBall" type="RigidBody2D"]
collision_layer = 2
collision_mask = 13
physics_material_override = ExtResource("1_0kw5n")
gravity_scale = 0.0
can_sleep = false
lock_rotation = true
custom_integrator = true
continuous_cd = 1
linear_damp_mode = 1
angular_damp_mode = 1
script = ExtResource("2_j34a3")
ui_resource = SubResource("Resource_fhoko")
ball_type = 1
cooldown = 15.0
acceleration_multiplier = 1.01
damage_color = Color(0.611765, 0, 0, 1)

[node name="AttributeComponent" type="Node" parent="."]
script = ExtResource("3_7vflf")
attribute_set = SubResource("Resource_pti4m")
metadata/_custom_type_script = "uid://3lqxnj5nr7f1"

[node name="Sprite2D" type="Sprite2D" parent="."]
material = ExtResource("3_v1ct7")
scale = Vector2(0.5, 0.5)
texture = ExtResource("3_mwx5r")

[node name="Trail" type="Line2D" parent="Sprite2D"]
top_level = true
z_index = -1
texture_repeat = 2
material = SubResource("ShaderMaterial_ck2q6")
instance_shader_parameters/overall_alpha = 1.0
width = 40.0
width_curve = SubResource("Curve_yi17e")
texture_mode = 2
joint_mode = 2
begin_cap_mode = 2
end_cap_mode = 2
script = ExtResource("4_v1ct7")
max_points = 20

[node name="CollisionShape2D" type="CollisionShape2D" parent="."]
visible = false
shape = SubResource("CircleShape2D_yi17e")

[node name="Abilities" type="Node" parent="."]

[node name="BloodAbility" type="Node" parent="Abilities"]
script = ExtResource("7_u0ia3")
debuff_template = ExtResource("8_xuec2")
ui_resource = SubResource("Resource_rw2wu")
metadata/_custom_type_script = "uid://pe82fwtsrv88"
